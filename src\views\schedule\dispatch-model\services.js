import request from '@/utils/request'

// 分页查询
export function getResvrDispPage(data) {
  return request({
    url: '/model/resvr-disp/page',
    method: 'post',
    data,
  })
}

// 删除
export function deleteResvrDisp(params) {
  return request({
    url: '/model/resvr-disp/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取降雨过程
export function getRainfallList(data) {
  return request({
    url: '/model/resvr-disp/rainfall/list',
    method: 'post',
    data,
  })
}

// 执行预报
export function forecast(data) {
  return request({
    url: '/model/resvr-disp/forecast',
    method: 'post',
    data,
  })
}

// 保存
export function saveResvrDisp(params) {
  return request({
    url: '/model/resvr-disp/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 详情
export function getResvrDisp(params) {
  return request({
    url: '/model/resvr-disp/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取模型结果
export function getDispRes(params) {
  return request({
    url: '/model/resvr-disp/getDispRes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
